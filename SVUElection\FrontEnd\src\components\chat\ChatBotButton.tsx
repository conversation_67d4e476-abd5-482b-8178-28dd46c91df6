import React, { useState, useEffect } from 'react';
import AiChatBot from './AiChatBot';

/**
 * زر لفتح وإغلاق المساعد الذكي
 */
const ChatBotButton: React.FC = () => {
  // حالة فتح/إغلاق المساعد
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);
  // حالة تحميل المكون
  const [isLoaded, setIsLoaded] = useState(false);

  // تأخير تحميل المكون لتجنب أي مشكلات أثناء تحميل الصفحة
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // فتح المساعد
  const handleOpenChatbot = () => {
    setIsChatbotOpen(true);
  };

  // إغلاق المساعد
  const handleCloseChatbot = () => {
    setIsChatbotOpen(false);
  };

  // إذا لم يتم تحميل المكون بعد، لا تعرض شيئاً
  if (!isLoaded) return null;

  return (
    <>
      {/* زر فتح المساعد */}
      {!isChatbotOpen && (
        <button
          onClick={handleOpenChatbot}
          className="fixed bottom-4 right-4 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 z-40"
          aria-label="فتح المساعد الذكي"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
        </button>
      )}

      {/* مكون المساعد الذكي */}
      <AiChatBot isOpen={isChatbotOpen} onClose={handleCloseChatbot} />
    </>
  );
};

export default ChatBotButton;
