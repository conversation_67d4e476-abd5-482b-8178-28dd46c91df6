import { Link, useLocation } from 'react-router-dom';
import { Vote, LogIn, LogOut, Settings, Shield, Menu, X } from 'lucide-react';
import { useAppContext } from '../contexts/AppContext';
import { useState, useEffect } from 'react';

export function Header() {
  const { state, connectWallet, disconnectWallet } = useAppContext();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // تتبع التمرير لإضافة تأثيرات عند التمرير
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // إغلاق القائمة المنسدلة عند تغيير المسار
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  return (
    <header className={`sticky top-0 z-50 bg-white ${scrolled ? 'shadow-md' : 'shadow-sm'} transition-all duration-300`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-3">
          {/* الشعار */}
          <div className="flex items-center gap-2">
            <Link to="/" className="flex items-center gap-2 transition-transform hover:scale-105">
              <Vote className="w-8 h-8 text-blue-600" />
              <h1 className="text-xl md:text-2xl font-bold text-gray-900">SVUElection</h1>
            </Link>
          </div>

          {/* رسالة الأمان - تظهر فقط على الشاشات المتوسطة والكبيرة */}
          <div className="hidden md:flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1.5 rounded-lg">
            <Shield className="w-5 h-5 text-green-600" />
            <span className="font-medium text-sm">تم تأمين هذا التطبيق باستخدام BlockChain</span>
          </div>

          {/* القائمة وزر الاتصال */}
          <div className="flex items-center gap-4">
            {/* القائمة للشاشات المتوسطة والكبيرة */}
            <nav className="hidden md:flex items-center gap-2">
              <Link
                to="/"
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${isActive('/') ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                الرئيسية
              </Link>
              <Link
                to="/elections"
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${isActive('/elections') ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                الانتخابات
              </Link>
              {state.user?.isAdmin && (
                <Link
                  to="/admin"
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${isActive('/admin') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-100'}`}
                >
                  <Settings className="w-4 h-4 inline ml-1" />
                  لوحة التحكم
                </Link>
              )}
            </nav>

            {/* زر الاتصال/الخروج */}
            {state.user ? (
              <div className="flex items-center gap-2">
                <div className="hidden md:block text-sm text-gray-700">
                  <span className="font-medium">{state.user.address.slice(0, 6)}...{state.user.address.slice(-4)}</span>
                  {state.user.isAdmin && (
                    <span className="mr-2 bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full text-xs">مسؤول</span>
                  )}
                </div>
                <button
                  onClick={disconnectWallet}
                  className="flex items-center gap-1 bg-red-600 text-white px-3 py-1.5 rounded-lg hover:bg-red-700 text-sm transition-colors"
                >
                  <LogOut className="w-4 h-4" />
                  <span className="hidden md:inline">خروج</span>
                </button>
              </div>
            ) : (
              <button
                onClick={() => connectWallet()}
                className="flex items-center gap-1 bg-blue-600 text-white px-3 py-1.5 rounded-lg hover:bg-blue-700 text-sm transition-colors"
              >
                <LogIn className="w-4 h-4" />
                <span className="hidden xs:inline">اتصال بالمحفظة</span>
                <span className="xs:hidden">اتصال</span>
              </button>
            )}

            {/* زر القائمة للأجهزة المحمولة */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-700 hover:bg-gray-100 focus:outline-none"
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* القائمة المنسدلة للأجهزة المحمولة */}
        {mobileMenuOpen && (
          <div className="md:hidden py-2 border-t border-gray-200">
            <nav className="flex flex-col space-y-1 py-2">
              <Link
                to="/"
                className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/') ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                الرئيسية
              </Link>
              <Link
                to="/elections"
                className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/elections') ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                الانتخابات
              </Link>
              {state.user?.isAdmin && (
                <Link
                  to="/admin"
                  className={`px-3 py-2 rounded-md text-sm font-medium ${isActive('/admin') ? 'bg-purple-50 text-purple-700' : 'text-gray-700 hover:bg-gray-100'}`}
                >
                  <Settings className="w-4 h-4 inline ml-1" />
                  لوحة التحكم
                </Link>
              )}
              {/* رسالة الأمان للأجهزة المحمولة */}
              <div className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-2 rounded-md mt-2">
                <Shield className="w-4 h-4 text-green-600" />
                <span className="text-xs font-medium">تم تأمين التطبيق باستخدام BlockChain</span>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}