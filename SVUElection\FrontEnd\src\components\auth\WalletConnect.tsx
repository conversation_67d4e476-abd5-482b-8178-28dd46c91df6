import React, { useState } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { AlertCircle } from 'lucide-react';

interface WalletConnectProps {
  onSuccess?: () => void;
}

export const WalletConnect: React.FC<WalletConnectProps> = ({ onSuccess }) => {
  const { connectWallet, disconnectWallet, state } = useAppContext();
  const [error, setError] = useState<string | null>(null);
  const [connecting, setConnecting] = useState(false);

  const handleConnect = async () => {
    try {
      setConnecting(true);
      setError(null);
      await connectWallet();
      if (onSuccess) onSuccess();
    } catch (err: any) {
      setError(err.message || 'فشل الاتصال بالمحفظة');
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = () => {
    disconnectWallet();
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
            
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}
      
      {state.user ? (
        <div>
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            <p className="font-medium">متصل بالمحفظة</p>
            <p className="text-sm font-mono break-all">{state.user.address}</p>
            {state.user.isAdmin && (
              <p className="mt-2 text-sm bg-yellow-100 px-2 py-1 rounded inline-block">
                مسؤول النظام
              </p>
            )}
          </div>
          
          <button
            onClick={handleDisconnect}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150"
          >
            قطع الاتصال
          </button>
        </div>
      ) : (
        <button
          onClick={handleConnect}
          disabled={connecting}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {connecting ? 'جاري الاتصال...' : 'اتصال بالمحفظة'}
        </button>
      )}
      
      <p className="text-sm text-gray-600 mt-4 text-center">
        يتطلب استخدام التطبيق اتصالاً بمحفظة MetaMask أو محفظة متوافقة مع Web3
      </p>
    </div>
  );
};
