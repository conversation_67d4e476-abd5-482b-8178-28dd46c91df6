import React, { useState } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { AlertCircle, CheckCircle, UserPlus } from 'lucide-react';
import { ethers } from 'ethers';

interface AdminVoterRegistrationProps {
  electionId: number;
  onSuccess?: () => void;
}

export const AdminVoterRegistration: React.FC<AdminVoterRegistrationProps> = ({
  electionId,
  onSuccess
}) => {
  const { registerVoter, state } = useAppContext();
  const [voterAddress, setVoterAddress] = useState('');
  const [nationalId, setNationalId] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!state.user?.isAdmin) {
      setError('يجب أن تكون مسؤولاً للقيام بهذه العملية');
      return;
    }

    if (!voterAddress || !ethers.utils.isAddress(voterAddress)) {
      setError('عنوان المحفظة غير صالح');
      return;
    }

    if (!nationalId || nationalId.length !== 11 || !/^\d+$/.test(nationalId)) {
      setError('الرقم الوطني يجب أن يتكون من 11 رقم');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('Admin registering voter:', {
        electionId,
        voterAddress,
        nationalId
      });

      // تحويل الرقم الوطني إلى رقم صحيح
      const nationalIdNumber = parseInt(nationalId);

      await registerVoter(electionId, voterAddress, nationalIdNumber);

      // تأخير قصير للتأكد من تحديث البلوكتشين
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess(true);
      setVoterAddress('');
      setNationalId('');

      if (onSuccess) onSuccess();
    } catch (err: any) {
      console.error('Admin voter registration error:', err);
      setError(err.message || 'فشل تسجيل الناخب');
    } finally {
      setLoading(false);
    }
  };

  if (!state.user?.isAdmin) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p className="font-medium">غير مصرح</p>
          <p className="text-sm">يجب أن تكون مسؤولاً للوصول إلى هذه الصفحة</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-4">
        <UserPlus className="w-12 h-12 text-blue-600" />
      </div>

      <h2 className="text-2xl font-bold text-center mb-4">تسجيل ناخب (للمسؤول)</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
          <AlertCircle className="w-5 h-5 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
          <CheckCircle className="w-5 h-5 mr-2" />
          <span>تم تسجيل الناخب بنجاح</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="voterAddress">
            عنوان محفظة الناخب
          </label>
          <input
            id="voterAddress"
            type="text"
            value={voterAddress}
            onChange={(e) => setVoterAddress(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="0x..."
            disabled={loading}
          />
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="nationalId">
            الرقم الوطني
          </label>
          <input
            id="nationalId"
            type="text"
            value={nationalId}
            onChange={(e) => setNationalId(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="أدخل الرقم الوطني المكون من 11 رقم"
            maxLength={11}
            disabled={loading}
          />
          <p className="text-xs text-gray-500 mt-1">
            الرقم الوطني يجب أن يتكون من 11 رقم
          </p>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'جاري التسجيل...' : 'تسجيل الناخب'}
        </button>
      </form>
    </div>
  );
};
