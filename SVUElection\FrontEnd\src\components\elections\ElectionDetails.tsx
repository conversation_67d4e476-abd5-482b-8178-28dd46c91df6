import React, { useState, useEffect } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { Clock, Calendar, AlertCircle, CheckCircle, PauseCircle, PlayCircle } from 'lucide-react';
import { CandidateCard } from './CandidateCard';
import { AuthTabs } from '../auth';
import { Election, Candidate } from '../../interfaces';

interface ElectionDetailsProps {
  electionId: number;
}

export const ElectionDetails: React.FC<ElectionDetailsProps> = ({ electionId }) => {
  const {
    state,
    vote,
    isVoterRegistered,
    hasVoted,
    pauseElection,
    resumeElection
  } = useAppContext();

  const [election, setElection] = useState<Election | null>(null);
  const [isRegistered, setIsRegistered] = useState(false);
  const [userHasVoted, setUserHasVoted] = useState(false);
  const [votingError, setVotingError] = useState<string | null>(null);
  const [votingSuccess, setVotingSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  // البحث عن الانتخابات بالمعرف
  useEffect(() => {
    const foundElection = state.elections.find(e => e.id === electionId);
    if (foundElection) {
      setElection(foundElection);
    }
  }, [electionId, state.elections]);

  // التحقق من تسجيل المستخدم وتصويته
  useEffect(() => {
    let isMounted = true;
    let intervalId: NodeJS.Timeout | null = null;

    const checkVoterStatus = async () => {
      if (!state.user || !election || !isMounted) return;

      try {
        console.log('ElectionDetails: Checking voter status for election:', electionId, 'user:', state.user.address);
        const registered = await isVoterRegistered(electionId, state.user.address);

        if (!isMounted) return;

        console.log('ElectionDetails: Voter registration status:', registered);
        setIsRegistered(registered);

        if (registered) {
          try {
            const voted = await hasVoted(electionId, state.user.address);
            if (!isMounted) return;

            console.log('ElectionDetails: Voter has voted status:', voted);
            setUserHasVoted(voted);
          } catch (voteErr) {
            console.error('Error checking vote status:', voteErr);
          }
        }
      } catch (err) {
        if (!isMounted) return;
        console.error('خطأ في التحقق من حالة الناخب:', err);
      }
    };

    // التحقق مرة واحدة فورًا
    checkVoterStatus();

    // إعادة التحقق كل 10 ثوانٍ للتأكد من تحديث الحالة
    // زيادة الفترة لتقليل الضغط على البلوكتشين
    intervalId = setInterval(checkVoterStatus, 10000);

    return () => {
      isMounted = false;
      if (intervalId) clearInterval(intervalId);
    };
  }, [electionId, state.user, election]);

  const handleVote = async (candidateId: number) => {
    console.log(`ElectionDetails: Attempting to vote for candidate ${candidateId} in election ${electionId}`);

    if (!state.user) {
      console.log('ElectionDetails: User not connected');
      setVotingError('يجب الاتصال بالمحفظة أولاً');
      return;
    }

    if (!isRegistered) {
      console.log('ElectionDetails: User not registered as voter');
      setVotingError('يجب التسجيل كناخب أولاً');
      return;
    }

    if (userHasVoted) {
      console.log('ElectionDetails: User has already voted');
      setVotingError('لقد قمت بالتصويت بالفعل');
      return;
    }

    // طباعة معلومات المرشح للتشخيص
    const candidate = election.candidates.find(c => c.id === candidateId);
    console.log('ElectionDetails: Candidate details:', candidate);

    if (!candidate) {
      console.error('ElectionDetails: Candidate not found');
      setVotingError('المرشح غير موجود');
      return;
    }

    try {
      setLoading(true);
      setVotingError(null);
      // استخدام الفهرس كمعرف للمرشح في العقد الذكي
      console.log(`ElectionDetails: Calling vote function with electionId=${electionId}, candidateId=${candidateId}`);
      await vote(electionId, candidateId);
      console.log('ElectionDetails: Vote successful');
      setVotingSuccess(true);
      setUserHasVoted(true);
    } catch (err: any) {
      console.error('ElectionDetails: Voting error:', err);
      setVotingError(err.message || 'فشل التصويت');
    } finally {
      setLoading(false);
    }
  };

  const handlePauseElection = async () => {
    if (!state.user?.isAdmin) return;

    try {
      setLoading(true);
      await pauseElection(electionId);
    } catch (err) {
      console.error('خطأ في إيقاف الانتخابات:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleResumeElection = async () => {
    if (!state.user?.isAdmin) return;

    try {
      setLoading(true);
      await resumeElection(electionId);
    } catch (err) {
      console.error('خطأ في استئناف الانتخابات:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!election) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-md">
        <p className="text-center text-gray-600">جاري تحميل تفاصيل الانتخابات...</p>
      </div>
    );
  }

  const now = Math.floor(Date.now() / 1000);
  const isActive = now >= election.startTime && now <= election.endTime && !election.isPaused;
  const hasStarted = now >= election.startTime;
  const hasEnded = now > election.endTime;

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('ar-SA');
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-4">{election.title}</h2>

        <div className="flex flex-wrap gap-4 mb-4">
          <div className="flex items-center text-gray-600">
            <Calendar className="w-5 h-5 ml-2" />
            <div>
              <p className="text-sm">بداية: {formatDate(election.startTime)}</p>
              <p className="text-sm">نهاية: {formatDate(election.endTime)}</p>
            </div>
          </div>

          <div className="flex items-center">
            {isActive ? (
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                نشط حالياً
              </span>
            ) : election.isPaused ? (
              <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <PauseCircle className="w-4 h-4 ml-1" />
                متوقف مؤقتاً
              </span>
            ) : hasEnded ? (
              <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                انتهى
              </span>
            ) : !hasStarted ? (
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                لم يبدأ بعد
              </span>
            ) : null}
          </div>
        </div>

        {state.user?.isAdmin && (
          <div className="flex gap-2 mb-4">
            {!election.isPaused ? (
              <button
                onClick={handlePauseElection}
                disabled={loading || hasEnded}
                className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                <PauseCircle className="w-4 h-4 inline ml-1" />
                إيقاف مؤقت
              </button>
            ) : (
              <button
                onClick={handleResumeElection}
                disabled={loading || hasEnded}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                <PlayCircle className="w-4 h-4 inline ml-1" />
                استئناف
              </button>
            )}
          </div>
        )}

        {votingError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <span>{votingError}</span>
          </div>
        )}

        {votingSuccess && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2" />
            <span>تم تسجيل صوتك بنجاح!</span>
          </div>
        )}

        {!isRegistered && !userHasVoted && (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-4">
            <p className="font-medium">أنت غير مسجل كناخب في هذه الانتخابات</p>
            <p className="text-sm">يجب التسجيل أولاً للتمكن من التصويت</p>
          </div>
        )}
      </div>

      {!isRegistered && !userHasVoted && (
        <div className="mb-6">
          <h3 className="text-xl font-bold mb-4">التسجيل للتصويت</h3>
          <AuthTabs electionId={electionId} />
        </div>
      )}

      <div>
        <h3 className="text-xl font-bold mb-4">المرشحون ({election.candidates.length})</h3>

        {election.candidates.length === 0 ? (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <p className="text-center text-gray-600">لا يوجد مرشحون في هذه الانتخابات</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {election.candidates.map((candidate) => (
              <CandidateCard
                key={candidate.id}
                candidate={candidate}
                onVote={() => handleVote(candidate.id)}
                canVote={isRegistered && !userHasVoted && isActive}
                loading={loading}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
