import React, { useState } from 'react';
import { useAppContext } from '../../contexts/AppContext';
import { Calendar, Clock, PauseCircle, ArrowRight } from 'lucide-react';
import { Election } from '../../interfaces';

interface ElectionsListProps {
  onSelectElection: (electionId: number) => void;
}

export const ElectionsList: React.FC<ElectionsListProps> = ({ onSelectElection }) => {
  const { state } = useAppContext();
  const [filter, setFilter] = useState<'all' | 'active' | 'upcoming' | 'ended'>('all');
  
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('ar-SA');
  };
  
  const now = Math.floor(Date.now() / 1000);
  
  const filteredElections = state.elections.filter((election) => {
    if (filter === 'all') return true;
    if (filter === 'active') return now >= election.startTime && now <= election.endTime && !election.isPaused;
    if (filter === 'upcoming') return now < election.startTime;
    if (filter === 'ended') return now > election.endTime;
    return true;
  });
  
  return (
    <div className="space-y-6">
      <div className="bg-white p-4 rounded-lg shadow-md">
        <div className="flex flex-wrap gap-2">
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              filter === 'all'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setFilter('all')}
          >
            جميع الانتخابات
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              filter === 'active'
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setFilter('active')}
          >
            نشطة حالياً
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              filter === 'upcoming'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setFilter('upcoming')}
          >
            قادمة
          </button>
          <button
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              filter === 'ended'
                ? 'bg-gray-100 text-gray-800'
                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
            }`}
            onClick={() => setFilter('ended')}
          >
            منتهية
          </button>
        </div>
      </div>
      
      {state.loading ? (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <p className="text-center text-gray-600">جاري تحميل الانتخابات...</p>
        </div>
      ) : filteredElections.length === 0 ? (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <p className="text-center text-gray-600">لا توجد انتخابات متاحة</p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredElections.map((election) => (
            <ElectionCard
              key={election.id}
              election={election}
              onSelect={() => onSelectElection(election.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface ElectionCardProps {
  election: Election;
  onSelect: () => void;
}

const ElectionCard: React.FC<ElectionCardProps> = ({ election, onSelect }) => {
  const now = Math.floor(Date.now() / 1000);
  const isActive = now >= election.startTime && now <= election.endTime && !election.isPaused;
  const hasStarted = now >= election.startTime;
  const hasEnded = now > election.endTime;
  
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('ar-US');
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
      <div className="p-4">
        <div className="flex justify-between items-start">
          <h3 className="text-xl font-bold mb-2">{election.title}</h3>
          
          <div>
            {isActive ? (
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                نشط حالياً
              </span>
            ) : election.isPaused ? (
              <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <PauseCircle className="w-4 h-4 ml-1" />
                متوقف مؤقتاً
              </span>
            ) : hasEnded ? (
              <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                انتهى
              </span>
            ) : !hasStarted ? (
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                لم يبدأ بعد
              </span>
            ) : null}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-4 mb-4 text-gray-600">
          <div className="flex items-center">
            <Calendar className="w-5 h-5 ml-2" />
            <div>
              <p className="text-sm">بداية: {formatDate(election.startTime)}</p>
              <p className="text-sm">نهاية: {formatDate(election.endTime)}</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <span className="text-sm">عدد المرشحين: {election.candidateCount}</span>
          </div>
        </div>
        
        <button
          onClick={onSelect}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 flex items-center justify-center"
        >
          عرض التفاصيل
          <ArrowRight className="w-5 h-5 mr-2" />
        </button>
      </div>
    </div>
  );
};
