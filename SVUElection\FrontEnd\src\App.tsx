import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Header } from './components/Header';
import { HomePage } from './pages/HomePage';
import { ElectionsPage } from './pages/ElectionsPage';
import AdminPage from './pages/AdminPage';
import ChatBotButton from './components/chat/ChatBotButton';
import { AppProvider } from './contexts/AppContext';
import { useAppContext } from './contexts/AppContext';

// Admin route guard component
const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAppContext();

  // Check both that user exists AND is admin
  if (!state.user || !state.user.isAdmin) {
    console.log('Redirecting: User is not admin');
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Auth route guard component
export const AuthRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { state } = useAppContext();

  if (!state.user) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

function AppContent() {
  // حالة تحميل المساعد الذكي
  const [chatbotLoaded, setChatbotLoaded] = React.useState(false);

  // تأخير تحميل المساعد الذكي حتى يتم تحميل التطبيق بالكامل
  React.useEffect(() => {
    // تأخير تحميل المساعد الذكي لمدة 2 ثانية
    const timer = setTimeout(() => {
      setChatbotLoaded(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/elections" element={<ElectionsPage />} />
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminPage />
            </AdminRoute>
          }
        />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* إضافة زر المساعد الذكي بعد تحميل التطبيق */}
      {chatbotLoaded && <ChatBotButton />}
    </div>
  );
}

function App() {
  return (
    <Router>
      <AppProvider>
        <AppContent />
      </AppProvider>
    </Router>
  );
}

export default App;





