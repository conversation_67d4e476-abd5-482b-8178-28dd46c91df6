import React, { useState } from 'react';
import { Upload, CheckCircle, AlertCircle } from 'lucide-react';

interface FileUploaderProps {
  onImageSelect: (imageUrl: string) => void;
}

export function FileUploader({ onImageSelect }: FileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const validateFile = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('يرجى اختيار ملف صورة فقط');
      return false;
    }
    if (file.size > 10 * 1024 * 1024) {
      setError('حجم الصورة يجب أن لا يتجاوز 10 ميجابايت');
      return false;
    }
    return true;
  };

  const handleFile = (file: File) => {
    setError(null);
    if (!validateFile(file)) return;

    // ضغط الصورة وتحويلها إلى Data URL بحجم أصغر
    // استخدام حجم أكبر وجودة أعلى للحفاظ على دقة الصورة
    compressImage(file, 400, 400, 0.8).then(compressedDataUrl => {
      // عرض الصورة المضغوطة
      setPreview(compressedDataUrl);

      // التحقق من حجم الصورة المضغوطة
      const sizeInKB = Math.round(compressedDataUrl.length / 1024);
      console.log(`Compressed image size: ${sizeInKB}KB`);

      if (sizeInKB > 200) {
        console.warn('Image is still too large after compression');
        setError('حجم الصورة كبير جداً. يرجى استخدام صورة أصغر.');
        return;
      }

      // إرسال رابط الصورة المضغوطة إلى المكون الأب
      console.log('Sending compressed image URL to parent component');
      onImageSelect(compressedDataUrl);
    }).catch(err => {
      console.error('Error compressing image:', err);
      setError('حدث خطأ أثناء معالجة الصورة');
    });
  };

  // دالة لضغط الصورة وتغيير حجمها
  const compressImage = (file: File, maxWidth: number, maxHeight: number, quality: number): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (readerEvent) => {
        const img = new Image();
        img.onload = () => {
          // حساب الأبعاد الجديدة مع الحفاظ على نسبة العرض إلى الارتفاع
          let width = img.width;
          let height = img.height;

          if (width > height) {
            if (width > maxWidth) {
              height = Math.round(height * maxWidth / width);
              width = maxWidth;
            }
          } else {
            if (height > maxHeight) {
              width = Math.round(width * maxHeight / height);
              height = maxHeight;
            }
          }

          // إنشاء canvas لرسم الصورة المضغوطة
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // تحويل الصورة إلى Data URL بجودة محددة
          // استخدام صيغة jpeg للحصول على جودة أفضل
          const dataUrl = canvas.toDataURL('image/jpeg', quality);

          console.log(`Original size: ${Math.round(file.size / 1024)}KB, Compressed size: ${Math.round(dataUrl.length / 1024)}KB`);
          resolve(dataUrl);
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };

        if (typeof readerEvent.target?.result === 'string') {
          img.src = readerEvent.target.result;
        } else {
          reject(new Error('Failed to read file'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files?.length) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      handleFile(e.target.files[0]);
    }
  };

  return (
    <div className="w-full">
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-blue-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onChange={handleFileInput}
          accept="image/*"
        />

        <div className="text-center">
          {preview ? (
            <div className="space-y-4">
              <img
                src={preview}
                alt="Preview"
                className="mx-auto h-32 w-32 object-cover rounded-lg"
              />
              <div className="flex flex-col items-center justify-center gap-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span>تم اختيار الصورة بنجاح</span>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="space-y-2">
                <p className="text-gray-600">
                  اسحب وأفلت الصورة هنا أو انقر للاختيار
                </p>
                <p className="text-sm text-gray-500">
                  PNG, JPG حتى 10 ميجابايت
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="mt-2 flex items-center gap-2 text-red-600 text-sm">
          <AlertCircle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
}