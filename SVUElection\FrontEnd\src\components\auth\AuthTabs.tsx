import React, { useState } from 'react';
import { WalletConnect } from './WalletConnect';
import { VoterRegistration } from './VoterRegistration';
import { useAppContext } from '../../contexts/AppContext';

interface AuthTabsProps {
  electionId: number;
  onSuccess?: () => void;
}

export const AuthTabs: React.FC<AuthTabsProps> = ({ electionId, onSuccess }) => {
  const [activeTab, setActiveTab] = useState<'connect' | 'register'>('connect');
  const { state } = useAppContext();
  
  const handleWalletConnectSuccess = () => {
    // بعد الاتصال بالمحفظة، انتقل إلى تبويب التسجيل
    setActiveTab('register');
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="flex border-b">
        <button
          className={`flex-1 py-3 px-4 text-center font-medium ${
            activeTab === 'connect'
              ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
          }`}
          onClick={() => setActiveTab('connect')}
        >
          اتصال المحفظة
        </button>
        <button
          className={`flex-1 py-3 px-4 text-center font-medium ${
            activeTab === 'register'
              ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
          }`}
          onClick={() => setActiveTab('register')}
          disabled={!state.user}
        >
          تسجيل ناخب جديد
        </button>
      </div>
      
      <div className="p-4">
        {activeTab === 'connect' ? (
          <WalletConnect onSuccess={handleWalletConnectSuccess} />
        ) : (
          <VoterRegistration electionId={electionId} onSuccess={onSuccess} />
        )}
      </div>
    </div>
  );
};
